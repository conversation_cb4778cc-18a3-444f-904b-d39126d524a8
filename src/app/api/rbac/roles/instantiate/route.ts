import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';
import { PREDEFINED_CUSTOM_ROLES } from '@/lib/rbac/roles';

// Schema for role instantiation
const instantiateRoleSchema = z.object({
  templateName: z.string().min(1, 'Template name is required'),
  customName: z.string().min(1, 'Custom name is required').max(50, 'Name too long'),
  customDescription: z.string().optional(),
  additionalPermissions: z.array(z.string()).default([]),
  removePermissions: z.array(z.string()).default([]),
});

/**
 * POST /api/rbac/roles/instantiate
 * Create a custom role from a predefined template
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canCreate = await hasPermission('create:role', context);
    if (!canCreate) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await req.json();
    const validatedData = instantiateRoleSchema.parse(body);

    // Check if template exists
    const template = PREDEFINED_CUSTOM_ROLES[validatedData.templateName];
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    // Check if role with custom name already exists
    const existingRole = await db.customRole.findFirst({
      where: {
        name: validatedData.customName,
        organizationId: user.organizationId,
      },
    });

    if (existingRole) {
      return NextResponse.json({ error: 'Role with this name already exists' }, { status: 409 });
    }

    // Calculate final permissions
    let finalPermissions = [...template.permissions];
    
    // Add additional permissions
    if (validatedData.additionalPermissions.length > 0) {
      finalPermissions = [...new Set([...finalPermissions, ...validatedData.additionalPermissions])];
    }
    
    // Remove specified permissions
    if (validatedData.removePermissions.length > 0) {
      finalPermissions = finalPermissions.filter(p => !validatedData.removePermissions.includes(p));
    }

    // Create role in transaction
    const result = await db.$transaction(async (tx) => {
      // Create the role
      const newRole = await tx.customRole.create({
        data: {
          name: validatedData.customName,
          description: validatedData.customDescription || template.description,
          organizationId: user.organizationId!,
          isSystemRole: false,
        },
      });

      // Add permissions if provided
      if (finalPermissions.length > 0) {
        const permissions = await tx.permission.findMany({
          where: { name: { in: finalPermissions } },
          select: { id: true, name: true },
        });

        await tx.rolePermission.createMany({
          data: permissions.map(permission => ({
            roleId: newRole.id,
            permissionId: permission.id,
          })),
        });
      }

      // Log the role creation
      await tx.auditLog.create({
        data: {
          type: 'ROLE_CREATED',
          description: `Role ${validatedData.customName} created from template ${validatedData.templateName}`,
          organizationId: user.organizationId!,
          metadata: {
            roleName: validatedData.customName,
            templateName: validatedData.templateName,
            permissions: finalPermissions,
            additionalPermissions: validatedData.additionalPermissions,
            removedPermissions: validatedData.removePermissions,
          } as any,
        },
      });

      return newRole;
    });

    logger.info(`Role ${validatedData.customName} created from template ${validatedData.templateName} for organization ${user.organizationId}`);

    return NextResponse.json({
      success: true,
      data: {
        id: result.id,
        name: result.name,
        description: result.description,
        templateUsed: validatedData.templateName,
        permissions: finalPermissions,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error instantiating role from template:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
