"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Key, Copy, CheckCircle } from "lucide-react";

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  roleType: 'system' | 'predefined' | 'custom';
  permissions: Permission[];
  canInstantiate?: boolean;
}

interface InstantiateRoleDialogProps {
  role: Role | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInstantiate: (templateName: string, customName: string, customDescription?: string) => Promise<void>;
}

export function InstantiateRoleDialog({ 
  role, 
  open, 
  onOpenChange, 
  onInstantiate 
}: InstantiateRoleDialogProps) {
  const [customName, setCustomName] = useState("");
  const [customDescription, setCustomDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!role || !role.canInstantiate) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!customName.trim()) return;

    setIsSubmitting(true);
    try {
      await onInstantiate(role.name, customName.trim(), customDescription.trim() || undefined);
      // Reset form
      setCustomName("");
      setCustomDescription("");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
      if (!newOpen) {
        // Reset form when closing
        setCustomName("");
        setCustomDescription("");
      }
    }
  };

  const groupPermissionsByCategory = (permissions: Permission[]) => {
    return permissions.reduce((acc, permission) => {
      const category = permission.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  };

  const groupedPermissions = groupPermissionsByCategory(role.permissions);

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Copy className="h-4 w-4" />
            Create Role from Template
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-6">
            {/* Template Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  Template: {role.displayName}
                  <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                    Predefined
                  </Badge>
                </CardTitle>
                <CardDescription>
                  {role.description || "No description provided"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Included Permissions ({role.permissions.length})</h4>
                  <div className="space-y-3">
                    {Object.entries(groupedPermissions).map(([category, permissions]) => (
                      <div key={category}>
                        <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                          {category.replace('_', ' ')} ({permissions.length})
                        </h5>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {permissions.map((permission) => (
                            <Badge key={permission.name} variant="secondary" className="text-xs">
                              {permission.displayName}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Custom Role Details */}
            <Card>
              <CardHeader>
                <CardTitle>Custom Role Details</CardTitle>
                <CardDescription>
                  Provide details for your new custom role based on this template
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="customName">Role Name *</Label>
                    <Input
                      id="customName"
                      value={customName}
                      onChange={(e) => setCustomName(e.target.value)}
                      placeholder="Enter a unique name for your role"
                      required
                      disabled={isSubmitting}
                    />
                    <p className="text-xs text-muted-foreground">
                      This will be the internal name used to identify the role
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customDescription">Description</Label>
                    <Textarea
                      id="customDescription"
                      value={customDescription}
                      onChange={(e) => setCustomDescription(e.target.value)}
                      placeholder="Describe the purpose and scope of this role (optional)"
                      rows={3}
                      disabled={isSubmitting}
                    />
                    <p className="text-xs text-muted-foreground">
                      Leave empty to use the template description
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Name: </span>
                    <span className="text-sm">{customName || "Enter role name"}</span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Description: </span>
                    <span className="text-sm">
                      {customDescription || role.description || "No description"}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Permissions: </span>
                    <span className="text-sm">{role.permissions.length} permissions from template</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </ScrollArea>

        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => handleOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            onClick={handleSubmit}
            disabled={!customName.trim() || isSubmitting}
          >
            {isSubmitting ? "Creating..." : "Create Role"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
