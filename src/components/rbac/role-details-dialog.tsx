"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Shield, 
  Key, 
  UserCog, 
  Users, 
  Calendar, 
  Hierarchy,
  CheckCircle,
  Info
} from "lucide-react";

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  roleType: 'system' | 'predefined' | 'custom';
  parentRole?: {
    id: string;
    name: string;
    displayName: string;
  };
  childRoles: Array<{
    id: string;
    name: string;
    displayName: string;
  }>;
  permissions: Permission[];
  userCount: number;
  users: Array<{
    name: string;
    email: string;
  }>;
  createdAt: string;
  updatedAt: string;
  canEdit: boolean;
  canDelete: boolean;
  canInstantiate?: boolean;
}

interface RoleDetailsDialogProps {
  role: Role | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function RoleDetailsDialog({ role, open, onOpenChange }: RoleDetailsDialogProps) {
  if (!role) return null;

  const getRoleTypeColor = (type: string) => {
    switch (type) {
      case 'system': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'predefined': return 'bg-green-100 text-green-800 border-green-200';
      case 'custom': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleTypeIcon = (type: string) => {
    switch (type) {
      case 'system': return <Shield className="h-4 w-4" />;
      case 'predefined': return <Key className="h-4 w-4" />;
      case 'custom': return <UserCog className="h-4 w-4" />;
      default: return <UserCog className="h-4 w-4" />;
    }
  };

  const groupPermissionsByCategory = (permissions: Permission[]) => {
    return permissions.reduce((acc, permission) => {
      const category = permission.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const groupedPermissions = groupPermissionsByCategory(role.permissions);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getRoleTypeIcon(role.roleType)}
            {role.displayName}
            <Badge 
              variant="outline" 
              className={`text-xs ${getRoleTypeColor(role.roleType)}`}
            >
              {role.roleType.charAt(0).toUpperCase() + role.roleType.slice(1)}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[70vh]">
          <div className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Role Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Name</label>
                    <p className="text-sm">{role.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Display Name</label>
                    <p className="text-sm">{role.displayName}</p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="text-sm">{role.description || "No description provided"}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Created</label>
                    <p className="text-sm flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(role.createdAt)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                    <p className="text-sm flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(role.updatedAt)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Role Hierarchy */}
            {(role.parentRole || role.childRoles.length > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Hierarchy className="h-4 w-4" />
                    Role Hierarchy
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {role.parentRole && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Parent Role</label>
                      <div className="mt-1">
                        <Badge variant="outline">{role.parentRole.displayName}</Badge>
                      </div>
                    </div>
                  )}
                  {role.childRoles.length > 0 && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Child Roles</label>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {role.childRoles.map((childRole) => (
                          <Badge key={childRole.id} variant="outline">
                            {childRole.displayName}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Users */}
            {role.roleType === 'custom' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Assigned Users ({role.userCount})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {role.users.length > 0 ? (
                    <div className="space-y-2">
                      {role.users.map((user, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <p className="text-sm font-medium">{user.name}</p>
                            <p className="text-xs text-muted-foreground">{user.email}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No users assigned to this role</p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Permissions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Permissions ({role.permissions.length})
                </CardTitle>
                <CardDescription>
                  Permissions granted to users with this role
                </CardDescription>
              </CardHeader>
              <CardContent>
                {Object.keys(groupedPermissions).length > 0 ? (
                  <div className="space-y-4">
                    {Object.entries(groupedPermissions).map(([category, permissions]) => (
                      <div key={category}>
                        <h4 className="text-sm font-medium mb-2 capitalize">
                          {category.replace('_', ' ')} ({permissions.length})
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {permissions.map((permission) => (
                            <div key={permission.name} className="p-2 border rounded text-sm">
                              <div className="font-medium">{permission.displayName}</div>
                              {permission.description && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {permission.description}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                        {Object.keys(groupedPermissions).indexOf(category) < Object.keys(groupedPermissions).length - 1 && (
                          <Separator className="mt-4" />
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No permissions assigned to this role</p>
                )}
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
